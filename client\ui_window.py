
from PySide6.QtWidgets import QMainWindow, QApplication, QFrame, QLabel, QGridLayout, QVBoxLayout, QHBoxLayout, QPushButton, QGraphicsView, QGraphicsScene, QMessageBox, QWidget, QLineEdit, QTabWidget, QTextEdit, QStackedWidget, QStackedWidget, QScrollArea, QSizePolicy, QGraphicsPixmapItem
from PySide6.QtGui import QIcon, QPainter, QPixmap, QBrush, QFont, QPixmap, QPen, QColor, QPainterPath, QPolygonF
from PySide6.QtCore import Qt, QEvent, QPointF, Property, QObject, QPropertyAnimation
from PySide6.QtSvgWidgets import QGraphicsSvgItem
import os
from threading import Timer

# 导入对话框类
from ui_dialog import Login, RoomNameDialog


# 自定义提示框widget
class CustomTooltip(QWidget):
    """自定义提示框widget（单例模式）"""

    _instance = None

    def __new__(cls, parent=None):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, parent=None):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return

        super().__init__(parent)
        self.setWindowFlags(Qt.WindowType.ToolTip | Qt.WindowType.FramelessWindowHint)
        self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground)

        # 创建标签显示文本
        self.label = QLabel()
        self.label.setWordWrap(True)

        # 布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.addWidget(self.label)

        # 标记已初始化
        self._initialized = True

    def show_tooltip(self, text, position, is_playing=0):
        """显示提示框"""
        # 根据房间状态设置主题颜色
        if is_playing == 1:
            # 游戏中房间：橙色主题
            style = """
                QLabel {
                    background-color: #4a3728;
                    color: #ffcc99;
                    border: 2px solid #b77f54;
                    border-radius: 3px;
                    padding: 8px;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei';
                }
            """
        else:
            # 准备中房间：青色主题
            style = """
                QLabel {
                    background-color: #1a4a47;
                    color: #99ffff;
                    border: 2px solid #2fd5c9;
                    border-radius: 3px;
                    padding: 8px;
                    font-size: 12px;
                    font-family: 'Microsoft YaHei';
                }
            """
        self.label.setStyleSheet(style)
        self.label.setText(text)
        self.adjustSize()  # 自动调整大小
        self.move(position)
        self.show()
        self.raise_()  # 确保在最上层

    def hide_tooltip(self):
        """隐藏提示框"""
        self.hide()


class GuideSigns(QObject):
    def __init__(self, num, scene_rect=None, my_code=0):
        super().__init__()
        # 初始化pixmap变量，确保在所有情况下都有值
        pixmap = None
        if num == 1:
            pixmap = QPixmap("./client/image/pick.svg")
        elif num == 2:
            pixmap = QPixmap("./client/image/start.svg")
        elif num == 3:
            pixmap = QPixmap("./client/image/game_over.svg")
        else:
            # 为未知的num值提供默认图像，或者创建空的QPixmap
            pixmap = QPixmap()  # 创建空的QPixmap作为默认值

        # 检查图片是否加载成功
        if pixmap and pixmap.isNull():
            print(f"警告: 无法加载图片文件 (num={num})")
            pixmap = QPixmap()  # 使用空的QPixmap

        self.pixmap_item = QGraphicsPixmapItem(pixmap)

        # 初始化动画遮罩相关属性
        self.mask_item = None
        self.scene_size = None

        # 动态计算位置和变换原点
        if scene_rect:
            # 场景中心位置
            center_x = scene_rect.width() / 2
            center_y = scene_rect.height() / 2

            # 图片的实际大小
            pixmap_rect = self.pixmap_item.boundingRect()

            # 只在开始游戏时(num=1)创建动画遮罩层
            if num == 1:
                # 保存场景大小，用于动画过程中重新创建pixmap
                self.scene_size = (int(scene_rect.width()), int(scene_rect.height()))

                # 创建初始遮罩层 - 与view_mask相同的视觉效果
                mask_pixmap = QPixmap(self.scene_size[0], self.scene_size[1])
                mask_pixmap.fill(QColor(10, 10, 10, 255))  # 颜色完全不透明，透明度由setOpacity控制
                self.mask_item = QGraphicsPixmapItem(mask_pixmap)
                self.mask_item.setPos(0, 0)
                self.mask_item.setZValue(0)  # 在背景之上，图标之下
                print("创建动画遮罩层，使用setOpacity控制")

            # 设置变换原点为图片中心
            self.pixmap_item.setTransformOriginPoint(pixmap_rect.width() / 2, pixmap_rect.height() / 2)

            # 设置位置为场景中心（考虑图片大小偏移）
            self.pixmap_item.setPos(center_x - pixmap_rect.width() / 2, center_y - pixmap_rect.height() / 2)
            self.pixmap_item.setZValue(1)  # 确保在遮罩层上方

            print(f"场景大小: {scene_rect.width()}x{scene_rect.height()}")
            print(f"图片大小: {pixmap_rect.width()}x{pixmap_rect.height()}")
            print(f"设置位置: ({center_x - pixmap_rect.width() / 2}, {center_y - pixmap_rect.height() / 2})")
        else:
            # 使用默认值（向后兼容）
            self.pixmap_item.setTransformOriginPoint(200, 90)
            self.pixmap_item.setPos(272.5, 382.5)

        if num == 3:
            self.pixmap_item.setRotation(0)
        else:
            self.pixmap_item.setRotation(-90 * my_code)
        self.animation()

    def _set_scale(self, value):
        """设置缩放值"""
        if hasattr(self, 'pixmap_item'):
            self.pixmap_item.setScale(value)

    def _set_opacity(self, value):
        """设置透明度值"""
        if hasattr(self, 'pixmap_item'):
            self.pixmap_item.setOpacity(value)

    def _set_mask_opacity(self, value):
        """设置遮罩层透明度值"""
        if hasattr(self, 'mask_item') and self.mask_item:
            self.mask_item.setOpacity(value)

    def animation(self):
        """创建动画效果"""
        # 设置初始状态
        self.pixmap_item.setScale(3)
        self.pixmap_item.setOpacity(0)

        # 遮罩层初始透明度：220/255，与view_mask相同的视觉效果
        mask_initial_opacity = 220.0 / 255.0
        if self.mask_item:
            self.mask_item.setOpacity(mask_initial_opacity)

        # 创建缩放动画 - 使用GuideSigns对象作为目标，通过属性方法控制pixmap_item
        self.anim1 = QPropertyAnimation(self, b'scale')
        self.anim1.setDuration(2000)
        self.anim1.setStartValue(3.0)
        self.anim1.setKeyValueAt(0.35, 0.8)
        self.anim1.setKeyValueAt(0.9, 0.8)
        self.anim1.setEndValue(3.0)

        # 创建透明度动画
        self.anim2 = QPropertyAnimation(self, b'opacity')
        self.anim2.setDuration(2000)
        self.anim2.setStartValue(0.0)
        self.anim2.setKeyValueAt(0.35, 1.0)
        self.anim2.setKeyValueAt(0.9, 1.0)
        self.anim2.setEndValue(0.0)

        # 创建遮罩层透明度动画 - 在最后时刻渐隐消失
        self.anim3 = QPropertyAnimation(self, b'mask_opacity')
        self.anim3.setDuration(2000)
        self.anim3.setStartValue(mask_initial_opacity)  # 开始时与view_mask相同的透明度
        self.anim3.setKeyValueAt(0.9, mask_initial_opacity)  # 90%时间内保持原透明度
        self.anim3.setEndValue(0.0)  # 最后10%时间渐隐到完全透明

        # 添加动画完成回调
        self.anim1.finished.connect(lambda: print("缩放动画完成"))
        self.anim2.finished.connect(lambda: print("透明度动画完成"))
        self.anim3.finished.connect(lambda: print("遮罩动画完成"))

    # 定义属性，让QPropertyAnimation可以操作
    scale = Property(float, fset=_set_scale)
    opacity = Property(float, fset=_set_opacity)
    mask_opacity = Property(float, fset=_set_mask_opacity)


class RoomItemWidget(QWidget):
    """自定义房间项widget，支持QPainter背景绘制"""

    def __init__(self, logger, room_name: str, status: int, mainwindow, parent=None):
        super().__init__(parent)
        self.logger = logger
        self.room_name = room_name
        self.mainwindow = mainwindow
        self.is_playing = status
        self.is_hovered = False  # 添加hover状态变量

        # 房间成员列表
        self.room_members = []

        # 座位锁定信息（用于头像绘制）, 索引代表座位编号
        self.seat_locks = []  # [[username, lock_status], ...] lock_status: 0=未锁定，1=已锁定

        # 房间级头像缓存（优先查找）
        self.room_avatar_cache = {}  # {username: QPixmap}

        # 默认头像路径
        self.default_avatar_path = "./client/image/user1.png"  # 使用 user1.png 作为默认

        # 初始化玩家显示信息
        self.players = "0/4 已锁定"  # 默认显示

        # 加载背景图片（根据房间状态选择不同背景）
        self.background_pixmap = None
        if self.is_playing == 1:
            # 正在游戏的房间使用room1.png
            self.background_pixmap = QPixmap("./client/image/room1.png")
            if self.background_pixmap.isNull():
                print("警告: 无法加载房间背景图片 room1.png")
        else:
            # 准备中的房间使用room2.png
            self.background_pixmap = QPixmap("./client/image/room2.png")
            if self.background_pixmap.isNull():
                print("警告: 无法加载房间背景图片 room2.png")

        # 所有房间都使用统一的座位锁定系统

        self.setFixedHeight(60)
        self.setup_ui()

        # 安装事件过滤器
        self.installEventFilter(self)

    def setup_ui(self):
        """设置UI布局"""
        # 设置基础样式（移除CSS hover，改用事件过滤器）
        base_style = """
            RoomItemWidget {
                border: 0px solid #6a6a6a;
                border-radius: 3px;
                margin: 2px;
            }
            QLabel {
                background-color: transparent;
                border: none;
            }
        """

        # 根据房间状态设置不同的背景色（现在所有房间都有背景图片）
        if self.is_playing == 1:
            # 正在游戏的房间 - 透明背景，让QPainter绘制的room1.png显示
            background_color = "rgba(90, 90, 90, 80)"  # 半透明，让背景图片透出
        else:
            # 准备中的房间 - 透明背景，让QPainter绘制的room2.png显示
            background_color = "rgba(90, 90, 90, 80)"  # 半透明，让背景图片透出

        self.setStyleSheet(base_style + f"""
            RoomItemWidget {{
                background-color: {background_color};
            }}
        """)

        # 创建布局
        main_layout = QHBoxLayout(self)

        # 为头像预留空间（统一设置）
        avatar_space = 185  # 固定预留空间，足够容纳4个头像
        main_layout.setContentsMargins(avatar_space, 8, 10, 8)
        main_layout.setSpacing(0)

        # 添加弹性空间，将文字推到右侧
        main_layout.addStretch()

        # 文字信息区域
        text_layout = QVBoxLayout()
        text_layout.setContentsMargins(0, 0, 0, 0)
        text_layout.setSpacing(2)

        # 房间名称
        self.name_label = QLabel(self.room_name)
        name_color = "#ffffff" if not self.is_playing == 1 else "#ffff00"  # 正在游戏的房间用黄色
        self.name_label.setStyleSheet(f"font-size: 12px; font-weight: bold; color: {name_color};")
        self.name_label.setAlignment(Qt.AlignmentFlag.AlignRight)  # 文字右对齐
        self.name_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 禁用鼠标事件

        # 玩家信息
        self.players_label = QLabel(self.players)
        players_color = "#cccccc" if not self.is_playing == 1 else "#ffcccc"  # 正在游戏的房间用浅红色
        self.players_label.setStyleSheet(f"font-size: 10px; color: {players_color};")
        self.players_label.setAlignment(Qt.AlignmentFlag.AlignRight)  # 文字右对齐
        self.players_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 禁用鼠标事件

        # 状态信息
        self.status_label = QLabel('正在游戏' if self.is_playing == 1 else '准备游戏')
        status_color = "#ff6b6b" if self.is_playing == 1 else "#2fd5c9"
        self.status_label.setStyleSheet(f"font-size: 10px; color: {status_color}; font-weight: bold;")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignRight)  # 文字右对齐
        self.status_label.setAttribute(Qt.WidgetAttribute.WA_TransparentForMouseEvents, True)  # 禁用鼠标事件

        text_layout.addWidget(self.status_label)
        text_layout.addWidget(self.players_label)
        text_layout.addWidget(self.name_label)

        main_layout.addLayout(text_layout)

    def eventFilter(self, obj, event):
        """事件过滤器，处理hover效果和工具提示"""
        if obj == self:
            if event.type() == QEvent.Type.Enter:
                self.is_hovered = True
                self._show_room_tooltip()
                self.update()  # 触发重绘
                return True
            elif event.type() == QEvent.Type.Leave:
                self.is_hovered = False
                CustomTooltip().hide_tooltip()
                self.update()  # 触发重绘
                return True
            elif event.type() == QEvent.Type.MouseButtonPress and event.button() == Qt.MouseButton.LeftButton:
                if self.is_playing == 1 or self.mainwindow.I_am_playing:
                    QMessageBox.warning(self, "提示", "游戏进行中，无法加入")
                    return True
                elif self.mainwindow.user_data['username'] in self.room_members:
                    QMessageBox.warning(self, "提示", "你已经在此房间里了")
                    return True
                elif self.mainwindow.I_locked_seat:
                    QMessageBox.warning(self, "提示", "你已经锁定了座位，无法加入其他房间")
                    return True
                else:
                    self.logger.info(f'点击了房间: {self.room_name}')
                    self.mainwindow.message_hub.send_to_business_thread({
                        'type': 'room_event',
                        'data': {
                            'type': 'enter_room',
                            'username': self.mainwindow.user_data['username'],
                            'room_name': self.room_name,
                            'current_room': self.mainwindow.my_room
                        }
                    })
                    return True
        return super().eventFilter(obj, event)

    def draw_star(self, painter, center_x, center_y, radius, color):
        """绘制五角星"""
        import math

        # 五角星的5个外顶点和5个内顶点
        points = []
        for i in range(10):
            angle = i * math.pi / 5 - math.pi / 2  # 从顶部开始
            if i % 2 == 0:
                # 外顶点
                r = radius
            else:
                # 内顶点
                r = radius * 0.4

            x = center_x + r * math.cos(angle)
            y = center_y + r * math.sin(angle)
            points.append(QPointF(x, y))

        # 创建五角星路径
        star_polygon = QPolygonF(points)

        # 绘制五角星
        painter.save()
        painter.setBrush(color)
        painter.setPen(QPen(color, 1))
        painter.drawPolygon(star_polygon)
        painter.restore()

    def _show_room_tooltip(self):
        """显示房间信息工具提示"""
        if not self.seat_locks:
            # 空房间显示基本信息
            tooltip_text = f"房间: {self.room_name}\n状态: {'正在游戏' if self.is_playing == 1 else '准备游戏'}\n座位: 0/4 已锁定"
        else:
            # 有锁定座位的房间显示详细信息
            tooltip_lines = [f"房间: {self.room_name}", f"状态: {'正在游戏' if self.is_playing == 1 else '准备游戏'}", ""]

            # 按座位索引排序显示
            for i in range(4):
                username = self.seat_locks[i][0]  # 座位锁定信息列表的第一个元素是用户名
                tooltip_lines.append(f"座位{i + 1}: {username}")
            tooltip_text = "\n".join(tooltip_lines)

        # 显示工具提示在房间右侧，考虑滚动区域边界
        room_rect = self.rect()
        # 计算房间右侧位置：房间右边缘 + 10px间距
        tooltip_x = room_rect.right() + 10
        # 初始Y位置：与房间顶部对齐
        tooltip_y = room_rect.top()

        # 转换为全局坐标
        local_point = QPointF(tooltip_x, tooltip_y)
        global_pos = self.mapToGlobal(local_point.toPoint())

        # 查找父级滚动区域并调整位置
        scroll_area = self._find_scroll_area()
        if scroll_area:
            # 获取滚动区域viewport的全局边界（这是实际可见区域）
            viewport = scroll_area.viewport()
            viewport_rect = viewport.rect()
            viewport_global_pos = viewport.mapToGlobal(viewport_rect.topLeft())
            scroll_top = viewport_global_pos.y()
            scroll_bottom = viewport_global_pos.y() + viewport_rect.height()

            # 预估提示框高度（临时创建来测量）
            temp_tooltip = CustomTooltip()
            temp_tooltip.label.setText(tooltip_text)
            temp_tooltip.adjustSize()
            tooltip_height = temp_tooltip.height()

            # 调整Y坐标以适应滚动区域边界
            # 如果房间顶部超过滚动区域顶部，以滚动区域顶部为准
            if global_pos.y() < scroll_top:
                global_pos.setY(scroll_top)

            # 如果提示框底部超过滚动区域底部，向上调整
            if global_pos.y() + tooltip_height > scroll_bottom:
                global_pos.setY(scroll_bottom - tooltip_height)

        CustomTooltip().show_tooltip(tooltip_text, global_pos, self.is_playing)

    def _find_scroll_area(self):
        """查找父级滚动区域"""
        parent = self.parent()
        while parent:
            if isinstance(parent, QScrollArea):
                return parent
            parent = parent.parent()
        return None

    def paintEvent(self, _):
        """重写绘制事件，手动处理hover效果和背景绘制"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        # 绘制背景色
        if self.is_hovered:
            # hover状态的背景
            painter.fillRect(self.rect(), QColor(106, 106, 106, 150))
        else:
            # 正常状态的背景
            if self.is_playing == 1:
                painter.fillRect(self.rect(), QColor(90, 90, 90, 80))
            else:
                painter.fillRect(self.rect(), QColor(90, 90, 90, 80))

        # 绘制背景图片
        if self.background_pixmap and not self.background_pixmap.isNull():
            # 缩放背景图片以适应widget大小
            scaled_bg = self.background_pixmap.scaled(
                self.width(), self.height(),
                Qt.AspectRatioMode.KeepAspectRatioByExpanding,
                Qt.TransformationMode.SmoothTransformation
            )

            # 计算绘制位置（居中）
            bg_x = (self.width() - scaled_bg.width()) // 2
            bg_y = (self.height() - scaled_bg.height()) // 2

            # 根据房间状态设置不同的透明度
            if self.is_playing == 1:
                painter.setOpacity(0.5)  # 正在游戏的房间：40%透明度，更明显
            else:
                painter.setOpacity(0.25)  # 准备中的房间：25%透明度，更柔和

            painter.drawPixmap(bg_x, bg_y, scaled_bg)
            painter.setOpacity(1.0)  # 恢复完全不透明

        # 绘制头像（统一的绘制逻辑）
        self._draw_room_avatars(painter)

        # 绘制2px深灰色外框（所有房间）
        painter.save()
        pen = QPen(QColor(80, 80, 80), 2)  # 更深的灰色，2px粗
        painter.setPen(pen)
        painter.setBrush(Qt.BrushStyle.NoBrush)
        painter.drawRoundedRect(self.rect().adjusted(1, 1, -1, -1), 3, 3)
        painter.restore()

        # 绘制hover边框（根据房间状态选择颜色）
        if self.is_hovered:
            if self.is_playing == 1:
                hover_color = QColor(183, 127, 84)  # 游戏中房间：橙色
            else:
                hover_color = QColor(47, 213, 201)  # 准备中房间：青色

            pen = QPen(hover_color, 3)  # 3px粗
            painter.setPen(pen)
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawRoundedRect(self.rect().adjusted(1, 1, -1, -1), 3, 3)  # 内缩避免与外框重叠


    # 房间成员管理（仅维护，不用于显示）
    def add_room_member(self, player_name):
        """添加房间成员（仅维护列表）"""
        if player_name not in self.room_members:
            self.room_members.append(player_name)
        print(f'添加房间成员: {player_name}, 当前成员: {self.room_members}')

    def remove_room_member(self, player_name):
        """移除房间成员（仅维护列表）"""
        if player_name in self.room_members:
            self.room_members.remove(player_name)
        for i in range(4):
            if self.seat_locks[i][0] == player_name:
                self.seat_locks[i][0] = ''
                self.seat_locks[i][1] = 0
                self.update()
        print(f'移除房间成员: {player_name}, 当前成员: {self.room_members}')

    def _update_locked_seats_display(self):
        """更新锁定座位数量显示和所有标签样式"""
        locked_count = 0
        for i in range(4):
            if self.seat_locks[i][1] == 1:
                locked_count += 1

        # 根据房间类型更新显示内容和样式
        if self.is_playing == 1:
            # 游戏中房间
            self.players = f"{self.seat_locks[0][0]}等4人"
            status_text = "正在游戏"
            # 游戏中房间的颜色样式
            name_color = "#ffff00"      # 黄色
            players_color = "#ffcccc"   # 浅红色
            status_color = "#ff6b6b"    # 红色
        else:
            # 准备中房间
            self.players = f"{locked_count}/4 已锁定"
            status_text = "准备游戏"
            # 准备中房间的颜色样式
            name_color = "#ffffff"      # 白色
            players_color = "#cccccc"   # 浅灰色
            status_color = "#2fd5c9"    # 青色

        # 更新所有标签的文本内容
        self.players_label.setText(self.players)
        self.status_label.setText(status_text)

        # 重新渲染所有标签的样式
        self.name_label.setStyleSheet(f"font-size: 12px; font-weight: bold; color: {name_color};")
        self.players_label.setStyleSheet(f"font-size: 10px; color: {players_color};")
        self.status_label.setStyleSheet(f"font-size: 10px; color: {status_color}; font-weight: bold;")

    def _load_user_avatar(self, username):
        """加载用户头像（三级查找策略）"""
        
        # 1. 先在房间头像缓存中查找
        if username in self.room_avatar_cache:
            return

        # 2. 在统一本地缓存文件夹中查找
        import os
        local_cache_path = f"./client/image/cache/avatars/{username}.png"
        if os.path.exists(local_cache_path):
            pixmap = QPixmap(local_cache_path)
            if not pixmap.isNull():
                self.room_avatar_cache[username] = pixmap
                return

        # 3. 使用默认头像
        default_pixmap = QPixmap(self.default_avatar_path)
        if not default_pixmap.isNull():
            self.room_avatar_cache[username] = default_pixmap
        else:
            # 最后的备用方案
            backup_pixmap = QPixmap("./client/image/user1.png")
            self.room_avatar_cache[username] = backup_pixmap

    def clear_avatar_cache(self):
        """清理房间头像缓存（游戏开始时调用）"""
        self.room_avatar_cache.clear()

    def _draw_room_avatars(self, painter):
        """统一的房间头像绘制方法"""
        if not self.seat_locks:
            # 没有锁定座位时只绘制五角星
            self._draw_star_only(painter)
            return

        # 头像大小等于房间高度
        avatar_size = self.height()

        # 计算15度倾斜的偏移量
        import math
        angle_rad = math.radians(15)  # 15度转弧度
        skew_offset = avatar_size * math.tan(angle_rad)  # 倾斜偏移量

        # 统一的平行四边形下底长度
        uniform_bottom_length = 40  # 固定下底长40px
        left_offset = 25  # 整体向右偏移25px

        # 根据房间状态确定颜色
        if self.is_playing == 1:
            line_color = QColor(160, 108, 68)  # 橙色
            star_color = QColor(184, 127, 84)  # 橙色
        else:
            line_color = QColor(47, 213, 201)  # 青色
            star_color = QColor(46, 213, 200)  # 青色

        # 绘制头像（按座位索引排列）
        for i in range(4):
            username, _ = self.seat_locks[i]
            if username:
                self._load_user_avatar(username)
                avatar = self.room_avatar_cache[username]

                # 等比例缩放头像
                scale_width = int(uniform_bottom_length + skew_offset)  # 40 + 16.08 ≈ 56px
                scaled_avatar = avatar.scaled(
                    scale_width, avatar_size,
                    Qt.AspectRatioMode.KeepAspectRatioByExpanding,
                    Qt.TransformationMode.SmoothTransformation
                )

                # 居中裁剪到需要的尺寸
                if scaled_avatar.width() > scale_width:
                    crop_x = (scaled_avatar.width() - scale_width) // 2
                    cropped_avatar = scaled_avatar.copy(crop_x, 0, scale_width, avatar_size)
                elif scaled_avatar.height() > avatar_size:
                    crop_y = (scaled_avatar.height() - avatar_size) // 2
                    cropped_avatar = scaled_avatar.copy(0, crop_y, scale_width, avatar_size)
                else:
                    cropped_avatar = scaled_avatar

                # 计算头像位置 - 按座位索引排列
                avatar_x = left_offset + i * uniform_bottom_length

                # 创建向右倾斜15度的平行四边形裁剪路径
                clip_path = QPainterPath()
                points = [
                    QPointF(avatar_x, avatar_size),                                    # 左下角
                    QPointF(avatar_x + skew_offset, 0),                               # 左上角（向右偏移）
                    QPointF(avatar_x + uniform_bottom_length + skew_offset, 0),       # 右上角
                    QPointF(avatar_x + uniform_bottom_length, avatar_size)            # 右下角
                ]

                # 创建多边形路径
                polygon = QPolygonF(points)
                clip_path.addPolygon(polygon)

                # 保存当前绘制状态
                painter.save()

                # 设置裁剪路径
                painter.setClipPath(clip_path)

                # 绘制头像
                painter.drawPixmap(avatar_x, 0, cropped_avatar)

                # 恢复绘制状态
                painter.restore()

                # 绘制分隔边线（左右边线）
                painter.save()
                pen = QPen(line_color, 4)  # 根据房间状态选择颜色，4px粗
                painter.setPen(pen)

                # 绘制左边线（从左下角到左上角）
                painter.drawLine(
                    QPointF(avatar_x, avatar_size),                    # 左下角
                    QPointF(avatar_x + skew_offset, 0)                 # 左上角
                )

                # 绘制右边线（从右下角到右上角）
                painter.drawLine(
                    QPointF(avatar_x + uniform_bottom_length, avatar_size),          # 右下角
                    QPointF(avatar_x + uniform_bottom_length + skew_offset, 0)       # 右上角
                )

                painter.restore()

        # 绘制五角星
        star_radius = int(15 * 0.5)  # 缩小50%，半径约7.5px
        self.draw_star(painter, 15, 15, star_radius, star_color)

    def _draw_star_only(self, painter):
        """只绘制五角星（用于空房间）"""
        # 根据房间状态确定颜色
        star_color = QColor(184, 127, 84) if self.is_playing == 1 else QColor(46, 213, 200)

        star_radius = int(15 * 0.5)  # 缩小50%，半径约7.5px
        self.draw_star(painter, 15, 15, star_radius, star_color)



class MainWindow(QMainWindow):  # 管理主窗口及相关组件

    def __init__(self, app, logger, message_hub):
        super().__init__()
        self.app = app
        self.logger = logger
        self.message_hub = message_hub
        self.message_hub.mainwindow = self
        self.message_hub.signals.room_event_response.connect(self.handle_room_event)
        self.user_data = {}
        self.my_room = ''  # 标记我处于哪个房间
        self.my_seat = None  # 标记我坐在哪个座位
        self.room_name_dialog = None

        # 维护房间对象的字典
        self.rooms_dict = {}  # {room_name: room_widget} 房间名到房间对象的映射

        # 确保头像缓存目录存在
        cache_dir = "./client/image/cache/avatars"
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)

        self.login_widget = Login(self)
        self.login_widget_on = True
        self.logger.info('初始化主窗口')
        self.init_ui()  # 用来实例化UI对象
        self.resize_ui()  # 对UI对象的缩放统一管理
        self._is_connected = False
        self.I_am_playing = False
        self.I_locked_seat = False

        self.logger.info('主窗口初始化完成')

    # 初始化创建UI对象，并作一些初始设置
    def init_ui(self):
        # 根据不同的桌面分辨率，确定适当的缩放系数
        screen = self.app.primaryScreen()
        self.scale = (screen.geometry().height() - 100) / 945 / 1
        self.logger.debug(f'设置窗口缩放系数: {self.scale}')
        
        # 创建主窗口，并设置标题及图标
        self.setWindowTitle("超级四国大战")
        self.setWindowIcon(QIcon(r'./client/image/4InWar.ico'))
        self.setStyleSheet("background-color: #2b2b2b; color: #ffffff;")
        
        # 创建左侧堆叠组件,用来装载下面的页面
        self.left_stack = QStackedWidget(self)

        # 第一页：大厅页面
        self.hall = QWidget()

        # 设置暗色调背景
        self.hall.setStyleSheet("background-color: #2b2b2b; color: #ffffff;  border: None;")

        hall_main_layout = QVBoxLayout(self.hall)
        hall_main_layout.setContentsMargins(0, 0, 0, 0)
        hall_main_layout.setSpacing(0)

        # 顶部标题区域 - 使用重叠的QLabel避免CSS继承问题
        title_widget = QWidget()
        title_widget.setFixedHeight(180)
        title_widget.setStyleSheet("background-color: transparent;")  # 容器透明

        # 背景图片QLabel - 使用布局管理器而不是固定几何位置
        background_label = QLabel(title_widget)

        # 创建布局管理器让背景图片自适应容器大小
        title_layout = QHBoxLayout(title_widget)
        title_layout.setContentsMargins(15, 10, 0, 0)
        title_layout.addWidget(background_label)

        # 加载PNG格式的背景图片
        pixmap = QPixmap("./client/image/banner.png")  # 使用PNG格式
        if not pixmap.isNull():
            background_label.setPixmap(pixmap)
            background_label.setScaledContents(True)  # 自动缩放图片
        else:
            # 如果图片加载失败，使用纯色背景
            background_label.setStyleSheet("background-color: #3c3c3c;")

        # 文字标题QLabel（重叠在背景上，透明背景）
        title_label = QLabel("游戏大厅", title_widget)
        title_label.setGeometry(50, 30, 400, 40)  # 绝对定位
        title_label.setStyleSheet("font-size: 33px; font-weight: Bold; color: #ffffff; background-color: transparent;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)

        # 副标题QLabel（重叠在背景上，透明背景）
        subtitle_label = QLabel("欢迎加入超级四国大战", title_widget)
        subtitle_label.setGeometry(50, 80, 400, 40)  # 绝对定位，下移到90
        subtitle_label.setStyleSheet("font-size: 13px; color: #cccccc; background-color: transparent;")
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignLeft)

        hall_main_layout.addWidget(title_widget)

        # 房间区域容器
        rooms_container = QWidget()
        rooms_container.setStyleSheet("background-color: #2b2b2b;")
        rooms_layout = QHBoxLayout(rooms_container)
        rooms_layout.setContentsMargins(15, 10, 0, 10)
        rooms_layout.setSpacing(15)

        # 左侧：正在进行的游戏房间
        playing_rooms_widget, self.playing_rooms_layout = self.create_room_list_widget("正在进行的游戏", "#a06c44")
        rooms_layout.addWidget(playing_rooms_widget)

        # 右侧：准备中的游戏房间
        waiting_rooms_widget, self.waiting_rooms_layout = self.create_room_list_widget("准备中的游戏", "#2fd5c9")
        rooms_layout.addWidget(waiting_rooms_widget)

        hall_main_layout.addWidget(rooms_container, 1)  # 占用剩余空间

        # 第二页：游戏视图
        
        # 创建场景用来布置游戏棋盘区域
        self.scene = GameScene(self.logger, self.message_hub.signals.scene_click_signal, './client/image/chessboard.svg')
        self.view = QGraphicsView()
        self.view.setFrameStyle(QFrame.Shape.NoFrame)
        self.view.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)  # 这两行避免显示view窗口的滑动条
        self.view.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.view.setRenderHint(QPainter.RenderHint.TextAntialiasing, True)
        self.view.setRenderHint(QPainter.RenderHint.Antialiasing, True)
        self.view.setScene(self.scene)  # 给视图窗口设置需显示的场景,默认场景总是以中心点对齐视口中心点的。
        
        # 创建游戏视图遮罩
        self.create_view_mask()

        # 创建包厢区
        self.seat_choose_frame = QFrame(self.view)
        self.seat_choose_frame.setFrameStyle(QFrame.Shape.Panel | QFrame.Shadow.Raised)
        self.seat_choose_frame.setLineWidth(2)
        self.seat_choose_frame.setMidLineWidth(0)
        self.seat_choose_frame.setStyleSheet('''
            QFrame#room {background-color: rgba(60, 60, 60, 1); border: 0px solid #6a6a6a;}
            QFrame#room QLabel {background-color: rgba(100, 100, 100, 1);}''')

        # 创建座位
        self.s0 = Seat(0, self, self.message_hub.send_to_business_thread)
        self.s1 = Seat(1, self, self.message_hub.send_to_business_thread)
        self.s2 = Seat(2, self, self.message_hub.send_to_business_thread)
        self.s3 = Seat(3, self, self.message_hub.send_to_business_thread)
        self.seats = [self.s0, self.s1, self.s2, self.s3]
        self.table = QLabel()

        # 座位布局
        room_grid = QGridLayout(self.seat_choose_frame)
        room_grid.setAlignment(Qt.AlignmentFlag.AlignHCenter | Qt.AlignmentFlag.AlignVCenter)
        room_grid.setContentsMargins(0, 0, 0, 0)
        room_grid.setSpacing(0)
        room_grid.addWidget(self.s0, 2, 1)
        room_grid.addWidget(self.s1, 1, 2)
        room_grid.addWidget(self.s3, 1, 0)
        room_grid.addWidget(self.s2, 0, 1)
        room_grid.addWidget(self.table, 1, 1)

        self.left_stack.addWidget(self.hall)
        self.left_stack.addWidget(self.view)


        # 创建右侧标签页
        self.tabs = QTabWidget()

        # 设置标签页样式 - 让标签按键更大更明显，使用系统颜色
        self.tabs.setStyleSheet("""
            QTabWidget::pane {
                border: 0px solid palette(mid);
                background-color: palette(window);
                padding: 0px;
            }

            QTabWidget::tab-bar {
                alignment: left;
                top: 5px;
            }

            QTabBar::tab {
                background-color: #514236;
                border: 0px solid palette(mid);
                border-bottom-color: palette(mid);
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                min-width: 60px;
                min-height: 15px;
                padding: 6px 8px;
                margin-right: 3px;
                font-size: 14px;
                font-weight: bold;
                color: #ffffff;
            }

            QTabBar::tab:selected {
                background-color: #a06c44;
                color: palette(highlighted-text);
                border-color: palette(highlight);
                border-bottom-color: palette(highlight);
                font-size: 15px;
            }

            QTabBar::tab:hover {
                background-color: #036c86;
                color: #ffffff;
                font-size: 15px;
            }

            QTabBar::tab:!selected {
                margin-top: 3px;
            }
        """)


        # 创建游戏大厅标签页
        self.game_hall_tab = self.create_game_hall_tab()
        self.tabs.addTab(self.game_hall_tab, "游戏大厅")

        # 创建房间座位标签页（初始化时不添加到标签页中）
        self.room_tab = self.create_room_tab()

        self.user_frame = QFrame()
        user_layout = QVBoxLayout(self.user_frame)
        user_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        user_layout.setSpacing(8)
        user_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        self.avatar_label = QLabel(self.user_frame)
        pixmap = QPixmap("./client/image/user1.png")
        self.avatar_label.setPixmap(pixmap)
        self.user_name_label = QLabel(self.user_frame)
        self.user_name_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        self.user_name_label.setTextFormat(Qt.TextFormat.RichText)
        self.user_info_label = QLabel(self.user_frame)
        self.user_info_label.setTextFormat(Qt.TextFormat.RichText)  # 启用富文本支持

        user_layout.addWidget(self.avatar_label)
        user_layout.addWidget(self.user_name_label)
        user_layout.addWidget(self.user_info_label)

        self.right_top_widget = QWidget()
        right_top_layout = QHBoxLayout(self.right_top_widget)
        right_top_layout.setContentsMargins(0, 5, 0, 0)  # 移除内边距
        right_top_layout.addWidget(self.tabs)
        right_top_layout.addWidget(self.user_frame)

        self.right_bottom_stack = QStackedWidget()

        # 第一页添加大厅聊天界面
        hall_chat_interface, self.hall_chat_display = self.create_chat_interface("大厅聊天室")
        self.right_bottom_stack.addWidget(hall_chat_interface)

        # 第二页添加房间聊天界面
        room_chat_interface, self.room_chat_display = self.create_chat_interface("房间聊天室")
        self.right_bottom_stack.addWidget(room_chat_interface)

        # 默认显示第一页
        self.left_stack.setCurrentIndex(0)
        self.right_bottom_stack.setCurrentIndex(0) 

        # 连接标签页切换信号
        self.tabs.currentChanged.connect(self.on_tab_changed)

        self.right_widget = QWidget(self)
        right_layout = QVBoxLayout(self.right_widget)
        right_layout.setContentsMargins(0, 0, 10, 10)  # 设置内边距
        right_layout.addWidget(self.right_top_widget)
        right_layout.addWidget(self.right_bottom_stack)

        # 创建中央组件
        central_widget = QWidget()
        central_layout = QHBoxLayout(central_widget)  # 设置给中央组件
        # 移除左对齐设置，让布局自然分配空间
        central_layout.setContentsMargins(0, 0, 0, 0)
        central_layout.setSpacing(20)

        # 设置组件的尺寸策略
        self.left_stack.setSizePolicy(QSizePolicy.Policy.Fixed, QSizePolicy.Policy.Fixed)
        self.right_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Expanding)

        central_layout.addWidget(self.left_stack)
        central_layout.addWidget(self.right_widget, 1)  # 拉伸因子为1，占据剩余空间

        # 设置中央组件
        self.setCentralWidget(central_widget)

    def show_room_tab(self, room_name: str):
        self.tabs.addTab(self.room_tab, '我的房间')
        self.room_chat_display.clear()

        # 设置房间名称标签
        if hasattr(self, 'room_name_label'):
            self.room_name_label.setText(room_name)

        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            for i in range(4):
                self.seats[i].room_name = room_name
                self.seats[i].user_data = room_widget.seat_locks[i]
                if self.seats[i].user_data[0] != '':
                    self.seats[i].hold()
                    if self.seats[i].user_data[1] == 1:
                        self.seats[i].lock()
                else:
                    self.seats[i].release()

    def remove_room_tab(self):
        self.tabs.removeTab(1)

    def on_tab_changed(self, index):
        """标签页切换事件处理"""
        tab_text = self.tabs.tabText(index)
        self.logger.info(f'标签页切换到: {tab_text} (索引: {index})')

        # 根据不同标签页控制左侧堆叠组件显示
        if index == 0:  # 游戏大厅标签页
            self.logger.debug('切换到游戏大厅标签页 - 显示欢迎页面')
            self.left_stack.setCurrentIndex(0)  # 显示欢迎页面
            self.right_bottom_stack.setCurrentIndex(0)  # 显示大厅聊天界面
        elif index == 1:  # 我的房间标签页
            self.logger.debug('切换到我的房间标签页 - 显示游戏视图')
            self.left_stack.setCurrentIndex(1)  # 显示游戏视图
            self.right_bottom_stack.setCurrentIndex(1)  # 显示房间聊天界面

    def create_game_hall_tab(self):
        """创建游戏大厅标签页"""
        # 创建功能区
        function_frame = QFrame()
        function_frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Sunken)
        function_frame.setLineWidth(0)
        function_frame.setMidLineWidth(0)

        # 创建按钮
        self.new_room = QPushButton("新建房间")
        self.new_room.setEnabled(True)
        self.new_room.clicked.connect(self.create_new_room)
        self.re_size_bt = QPushButton('窗口变小')
        self.re_size_bt.setEnabled(False)
        self.quit_bt = QPushButton('退出游戏')
        self.quit_bt.clicked.connect(self.close)

        # 设置按键字体为雅黑
        for button in [self.new_room, self.re_size_bt, self.quit_bt]:
            button_font = button.font()
            button_font.setFamily("Microsoft YaHei")
            button.setFont(button_font)
            button.setMaximumWidth(160)

        # 布局
        vb1 = QVBoxLayout(function_frame)
        vb1.setAlignment(Qt.AlignmentFlag.AlignTop)
        vb1.setContentsMargins(0, 20, 0, 0)  # 移除内边距
        vb1.setSpacing(6)  # 功能区按键之间增加间距

        vb1.addWidget(self.new_room)
        vb1.addWidget(self.re_size_bt)
        vb1.addWidget(self.quit_bt)

        # 添加弹性空间，为以后插入其他frame预留位置
        vb1.addStretch()

        return function_frame
    
    def create_new_room(self):
        """创建新房间对话框"""
        self.logger.info('弹出创建新房间对话框')
        self.room_name_dialog = RoomNameDialog(self)
        self.room_name_dialog.exec()  # 对话框内部处理所有逻辑

    def create_room_tab(self):
        """创建房间信息标签页"""
        tab = QWidget()
        main_layout = QVBoxLayout(tab)
        main_layout.setContentsMargins(0, 10, 0, 10)  # 去除标签页内容的内边距

        # 创建房间名称标签
        self.room_name_label = QLabel("房间名称")
        self.room_name_label.setStyleSheet("""
            font-size: 18px;
            font-weight: bold;
            color: #2fd5c9;
            padding: 10px;
            background-color: #404040;
            border-radius: 5px;
            margin-bottom: 10px;
        """)
        self.room_name_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(self.room_name_label)

        # 创建功能区
        function_frame = QFrame()
        function_frame.setFrameStyle(QFrame.Shape.Box | QFrame.Shadow.Sunken)
        function_frame.setLineWidth(0)
        function_frame.setMidLineWidth(0)

        # 创建按钮
        self.lock_bt = QPushButton("锁定座位")
        self.lock_bt.setEnabled(True)
        self.lock_bt.clicked.connect(lambda: self.seat_switcher(1))
        self.unlock_bt = QPushButton("解锁座位")
        self.unlock_bt.setEnabled(True)
        self.unlock_bt.clicked.connect(lambda: self.seat_switcher(0))
        self.re_size_bt = QPushButton('窗口变小')
        self.re_size_bt.setEnabled(False)
        self.quit_bt = QPushButton('退出游戏')
        self.quit_bt.clicked.connect(self.close)

        # 设置按键字体为雅黑
        for button in [self.lock_bt, self.unlock_bt, self.re_size_bt, self.quit_bt]:
            button_font = button.font()
            button_font.setFamily("Microsoft YaHei")
            button.setFont(button_font)

        # 功能区布局
        function_layout = QVBoxLayout(function_frame)
        function_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        function_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        function_layout.setSpacing(6)  # 功能区按键之间增加间距

        # 按钮行布局
        button_row1 = QHBoxLayout()
        button_row1.addWidget(self.lock_bt)
        button_row1.addWidget(self.unlock_bt)

        button_row2 = QHBoxLayout()
        button_row2.addWidget(self.re_size_bt)

        button_row3 = QHBoxLayout()
        button_row3.addWidget(self.quit_bt)

        function_layout.addLayout(button_row1)
        function_layout.addLayout(button_row2)
        function_layout.addLayout(button_row3)

        # 将顶部布局添加到主布局
        main_layout.addWidget(function_frame)

        # 添加弹性空间，为以后插入其他frame预留位置
        main_layout.addStretch()

        return tab
    
    def seat_switcher(self, switch: int):
        if self.my_seat == None:
            return
        elif switch == 1 and self.I_locked_seat == True:
            return
        elif switch == 0 and self.I_locked_seat == False:
            return
        message = {
            'type': 'room_event',
            'data':{
                'type': 'seat_switch',
                'switch': switch,
                'seat_code': self.my_seat,
                'click_user': self.user_data['username'],
                'room_name': self.my_room
            }
        }
        self.message_hub.send_to_business_thread(message)

    
    def create_chat_interface(self, tab_name=""):
        """创建聊天界面"""
        chat_frame = QFrame()

        chat_layout = QVBoxLayout(chat_frame)
        chat_layout.setContentsMargins(0, 0, 0, 0)  # 移除内边距
        chat_layout.setSpacing(8)  # 聊天显示框和输入框之间增加间距

        chat_name = QLabel(tab_name)
        chat_name.setAlignment(Qt.AlignmentFlag.AlignLeft)

        # 设置聊天标题字体大小和颜色
        title_font = chat_name.font()
        title_font.setPointSize(14)  # 设置较大的字体大小
        title_font.setFamily("Microsoft YaHei")  # 设置雅黑字体
        title_font.setWeight(QFont.Weight.Bold)  # 设置粗体
        chat_name.setFont(title_font)
        chat_name.setStyleSheet("color: #888888;")  # 设置标题颜色

        chat_layout.addWidget(chat_name)

        # 聊天显示区
        chat_display = QTextEdit()
        chat_display.setReadOnly(True)
        chat_display.setMinimumHeight(150)  # 设置最小高度，确保基本可用性
        # 移除最大高度限制，允许自适应扩展
        chat_display.setPlaceholderText("聊天记录将在这里显示...")

        # 设置聊天显示区字体大小
        chat_font = chat_display.font()
        chat_font.setPointSize(10)  # 增大字体
        chat_font.setFamily("Microsoft YaHei")  # 设置雅黑字体
        chat_font.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, 1.2)  # 设置字符间距，增加1.2像素
        chat_display.setFont(chat_font)

        # 设置聊天显示区样式（圆角处理）
        chat_display.setStyleSheet("""
            QTextEdit {
                border-radius: 4px;
                padding: 10px;
                background-color: #333333;
                color: #ffffff;
                border: 0px solid #555555;
            }
        """)

        # 添加一些示例消息（身份提示加粗换色，增加行距）
        chat_display.append('<div style="margin-bottom: 4px;"><b style="color: #d32f2f; font-size: 15px; font-weight: bold;">系统 :</b> 欢迎进入4inWar游戏!</div>')
        chat_display.append('<div style="margin-bottom: 4px;"><b style="color: #1976d2; font-size: 15px; font-weight: bold;">玩家1 :</b> 大家好!</div>')

        # 输入区域
        input_layout = QHBoxLayout()
        input_layout.setContentsMargins(0, 0, 0, 0)  # 设置边距与聊天显示区一致
        input_layout.setSpacing(8)  # 输入框和发送按钮之间的间距

        # 聊天输入框
        chat_input = QLineEdit()
        chat_input.setPlaceholderText("输入消息...")

        # 设置输入框高度和字体
        chat_input.setMinimumHeight(32)  # 增加输入框高度
        input_font = chat_input.font()
        input_font.setPointSize(10)  # 增大字体
        input_font.setFamily("Microsoft YaHei")  # 设置雅黑字体
        input_font.setLetterSpacing(QFont.SpacingType.AbsoluteSpacing, 1.2)  # 设置字符间距，增加1.2像素
        chat_input.setFont(input_font)

        # 设置输入框样式（圆角处理和背景色）
        chat_input.setStyleSheet("""
            QLineEdit {
                background-color: #333333;
                border-radius: 4px;
                padding: 4px;
                color: #ffffff;
                border: 0px solid #555555;
            }
        """)

        # 发送按钮
        send_button = QPushButton("发送")
        send_button.setMinimumWidth(80)  # 设置按钮最小宽度
        send_button.setMinimumHeight(32)  # 与输入框高度匹配

        # 设置按钮字体
        button_font = send_button.font()
        button_font.setPointSize(11)  # 增大字体
        button_font.setBold(True)  # 加粗
        button_font.setFamily("Microsoft YaHei")  # 设置雅黑字体
        send_button.setFont(button_font)

        # 设置按钮样式（包括文字颜色和hover效果）
        send_button.setStyleSheet("""
            QPushButton {
                color: white;
                background-color: #a06c44;
                border: none;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #036c86;
                font-size: 16px;
            }
        """)

        # 创建发送消息的函数
        def send_message():
            message = chat_input.text().strip()
            if message:
                # 添加消息到显示区（身份提示加粗换色，增加行距）
                chat_display.append(f'<div style="margin-bottom: 4px;"><b style="color: #a06c44; font-size: 15px; font-weight: bold;">我 :</b> {message}</div>')
                # 清空输入框
                chat_input.clear()
                # 滚动到底部
                chat_display.verticalScrollBar().setValue(
                    chat_display.verticalScrollBar().maximum()
                )

        # 绑定发送事件
        chat_input.returnPressed.connect(send_message)
        send_button.clicked.connect(send_message)

        input_layout.addWidget(chat_input)
        input_layout.addWidget(send_button)

        # 添加到聊天布局
        chat_layout.addWidget(chat_display, 1)  # stretch factor = 1，聊天显示区占用剩余空间
        chat_layout.addLayout(input_layout)  # 输入区固定在底部

        return chat_frame, chat_display

    def create_room_list_widget(self, title, title_color):
        """创建房间列表组件，返回容器widget和房间列表布局"""
        container = QWidget()
        container.setStyleSheet(f"background-color: #333333; border: none;")

        layout = QVBoxLayout(container)
        layout.setContentsMargins(10, 10, 0, 0)
        layout.setSpacing(10)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"font-size: 16px; font-weight: bold; color: {title_color}; margin-bottom: 0px;")
        title_label.setAlignment(Qt.AlignmentFlag.AlignLeft)
        layout.addWidget(title_label)

        # 标题下方的白色分隔线
        separator_line = QFrame()
        separator_line.setFrameShape(QFrame.Shape.HLine)
        separator_line.setFrameShadow(QFrame.Shadow.Plain)
        separator_line.setStyleSheet("background-color: #404040; max-height: 1px; margin-right: 18px;")
        layout.addWidget(separator_line)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                background-color: #333333;
                width: 8px;
                border-radius: 2px;
                border: none;
            }}
            QScrollBar::handle:vertical {{
                background-color: #4d4d4d;
                border-radius: 2px;
                min-height: 20px;
                border: none;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {title_color};
            }}
            QScrollBar::add-line:vertical {{
                background: none;
                height: 0px;
                border: none;
            }}
            QScrollBar::sub-line:vertical {{
                background: none;
                height: 0px;
                border: none;
            }}
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: #3c3c3c;
                border: none;
            }}
        """)

        # 房间列表容器
        rooms_list_widget = QWidget()
        rooms_list_layout = QVBoxLayout(rooms_list_widget)
        rooms_list_layout.setContentsMargins(0, 0, 10, 0)  # 右侧增加10px间距，避免紧贴滚动条
        rooms_list_layout.setSpacing(10)
        rooms_list_layout.setAlignment(Qt.AlignmentFlag.AlignTop)  # 设置向上对齐
        scroll_area.setWidget(rooms_list_widget)
        layout.addWidget(scroll_area, 1)
        return container, rooms_list_layout

    def create_view_mask(self):
        """创建游戏视图遮罩"""
        self.view_mask = QWidget(self.view)
        self.view_mask.setStyleSheet("""
            background-color: rgba(10, 10, 10, 220);
        """)
        
        # 添加等待提示
        mask_layout = QVBoxLayout(self.view_mask)
        mask_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        waiting_label = QLabel("请选择座位，全员锁定后开始游戏")
        waiting_label.setStyleSheet("""
            color: #ffffff;
            font-size: 20px;
            font-weight: bold;
            background-color: transparent;
        """)
        waiting_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        mask_layout.addSpacing(100)
        mask_layout.addWidget(waiting_label)
        mask_layout.addStretch()

    def show_guide_signs(self, num):
        print(f"显示引导标志: num={num}")

        # 如果已经有guide_signs存在，先清理它
        if hasattr(self, 'guide_signs') and self.guide_signs is not None:
            print("清理之前的guide_signs")
            # 取消之前的定时器
            if hasattr(self, 'guide_timer') and self.guide_timer.is_alive():
                self.guide_timer.cancel()
            if hasattr(self, 'activate_timer') and self.activate_timer.is_alive():
                self.activate_timer.cancel()
            self.clean_guide_signs()

        # 获取场景矩形信息并传递给GuideSigns
        if self.my_seat:
            scene_rect = self.scene.sceneRect()
            self.guide_signs = GuideSigns(num, scene_rect, self.my_seat)

        # 先添加遮罩层（如果存在），再添加图标
        if self.guide_signs.mask_item:
            self.scene.addItem(self.guide_signs.mask_item)
            print(f"添加遮罩层到场景")

        self.scene.addItem(self.guide_signs.pixmap_item)
        print(f"添加到场景后: 缩放={self.guide_signs.pixmap_item.scale()}, 透明度={self.guide_signs.pixmap_item.opacity()}")
        print(f"图标位置: ({self.guide_signs.pixmap_item.pos().x()}, {self.guide_signs.pixmap_item.pos().y()})")

        print("启动动画...")
        self.guide_signs.anim1.start()
        self.guide_signs.anim2.start()
        if hasattr(self.guide_signs, 'anim3'):
            self.guide_signs.anim3.start()
            print(f"动画状态: anim1={self.guide_signs.anim1.state()}, anim2={self.guide_signs.anim2.state()}, anim3={self.guide_signs.anim3.state()}")
        else:
            print(f"动画状态: anim1={self.guide_signs.anim1.state()}, anim2={self.guide_signs.anim2.state()}")

        # 保存定时器引用，便于管理
        self.guide_timer = Timer(3, self.clean_guide_signs)
        self.guide_timer.start()
        if num == 1:
            self.activate_timer = Timer(3, self.scene.activate)
            self.activate_timer.start()
        
    def clean_guide_signs(self):
        # 检查guide_signs是否存在，避免重复清理或竞态条件
        if not hasattr(self, 'guide_signs') or self.guide_signs is None:
            print("clean_text: guide_signs已经被清理或不存在")
            return
        try:
            # 移除图标
            if hasattr(self.guide_signs, 'pixmap_item') and self.guide_signs.pixmap_item:
                self.scene.removeItem(self.guide_signs.pixmap_item)
                print("移除动画图标")

            # 移除遮罩层（如果存在）
            if hasattr(self.guide_signs, 'mask_item') and self.guide_signs.mask_item:
                self.scene.removeItem(self.guide_signs.mask_item)
                print("移除动画遮罩层")

            # 清理对象
            del self.guide_signs

            # 清理定时器引用
            if hasattr(self, 'guide_timer'):
                del self.guide_timer
            if hasattr(self, 'activate_timer'):
                del self.activate_timer
            print("动画清理完成")

        except Exception as e:
            print(f"clean_text执行出错: {e}")
            # 即使出错也要尝试清理对象引用
            if hasattr(self, 'guide_signs'):
                try:
                    del self.guide_signs
                except:
                    pass


    # =====================基础房间管理接口（统一使用房间名作为参数）====================

    def create_room(self, room_info: dict, room_locked_seats: dict):
        """创建游戏房间（服务器通知）"""
        for room_name, room_data in room_info.items():
            try:
                is_playing = room_data.get('is_playing', 0)
                members = room_data.get('members', [])
                member_count = room_data.get('member_count', 0)
                locked_seats = room_locked_seats.get(room_name, [['', 0], ['', 0], ['', 0], ['', 0]])
                if room_name not in self.rooms_dict:
                    room_widget = RoomItemWidget(self.logger, room_name, is_playing, self)
                    if is_playing == 1:
                        self.playing_rooms_layout.addWidget(room_widget)
                    else:
                        self.waiting_rooms_layout.addWidget(room_widget)
                    self.rooms_dict[room_name] = room_widget
                    room_widget.room_members = members
                    room_widget.seat_locks = locked_seats
                    self.logger.info(f'创建房间: {room_name}, 成员: {members}, 人数: {member_count}, 状态：{is_playing}')
                    # 处理UI显示
                    room_widget._update_locked_seats_display()
                    room_widget.update()
            except Exception as e:
                self.logger.error(f'创建房间 {room_name} 的UI元素时出错: {e}')
        
    def remove_room(self, room_name: str):
        """根据房间名移除房间（服务器通知）"""
        room_widget = self.rooms_dict.get(room_name)
        if not room_widget:
            return False

        # 从字典中移除
        del self.rooms_dict[room_name]

        # 根据房间的游戏状态判断从哪个布局移除
        if hasattr(room_widget, 'is_playing') and room_widget.is_playing == 1:
            # 正在游戏的房间，从左侧列表移除
            if hasattr(self, 'playing_rooms_layout'):
                self.playing_rooms_layout.removeWidget(room_widget)
        else:
            # 准备中的房间，从右侧列表移除
            if hasattr(self, 'waiting_rooms_layout'):
                self.waiting_rooms_layout.removeWidget(room_widget)

        # 删除widget对象
        room_widget.deleteLater()
        return True

    def move_room_to_playing(self, room_name: str, members: list):
        """将准备房间移动到游戏列表（服务器通知游戏开始）"""
        room_widget = self.rooms_dict.get(room_name)
        if room_widget:
            # 更新房间状态
            room_widget.is_playing = 1
            room_widget.room_members = members
            # 从准备列表移除
            self.waiting_rooms_layout.removeWidget(room_widget)
            # 添加到游戏列表
            self.playing_rooms_layout.addWidget(room_widget)
            # 更新UI显示
            room_widget._update_locked_seats_display()
            room_widget.update()
            return True
        return False

    def move_room_to_waiting(self, room_name: str):
        """将游戏房间移动到准备列表（服务器通知游戏结束）"""
        room_widget = self.rooms_dict.get(room_name)
        if room_widget:
            # 从游戏列表移除
            self.playing_rooms_layout.removeWidget(room_widget)
            # 添加到准备列表
            self.waiting_rooms_layout.addWidget(room_widget)
            # 更新房间状态
            room_widget.is_playing = 0
            # 更新UI显示
            room_widget._update_locked_seats_display()
            room_widget.update()
            return True
        return False

    def get_room_by_name(self, room_name):
        """根据房间名获取房间对象"""
        return self.rooms_dict.get(room_name)


    # ==================房间消息处理方法====================

    def handle_room_event(self, message):
        """处理房间相关事件的统一入口"""
        data = message.get("data", {})
        event_type = data.get("type")

        if event_type == "room_create_request":
            self._handle_room_request_response(data)
        elif event_type == "room_created":
            self._handle_room_create(data)
        elif event_type == "player_enter_room":
            self._handle_player_enter_room(data)
        elif event_type == "player_leave_room":
            self._handle_player_leave_room(data)
        elif event_type == 'seat_holded':
            self._handle_seat_holded(data)
        elif event_type == 'seat_released':
            self._handle_seat_released(data)
        elif event_type == 'ignored':
            return
        elif event_type == "seat_switch":
            self._handle_seat_switch(data)

    def _handle_room_request_response(self, data):
        """处理服务器对自己创建房间申请的响应，只负责重名处理和成功后关闭创建房间对话框，房间创建由_handle_room_create处理"""
        if not self.room_name_dialog:
            return
        if not self.room_name_dialog.is_waiting:
            return  # 如果不在等待状态，忽略响应

        # 停止定时器
        self.room_name_dialog.timeout_timer.stop()
        self.room_name_dialog.countdown_timer.stop()

        success = data.get('success', False)
        error_message = data.get('message', '未知错误')

        self.logger.info(f'收到创建房间响应: success={success}, room_name={self.room_name_dialog.room_name}')

        if success:
            # 创建成功，关闭对话框
            self.logger.info(f'房间创建成功: {self.room_name_dialog.room_name}')
            self.room_name_dialog.accept()
            # 调用主窗口方法显示房间标签页
            self.show_room_tab(self.room_name_dialog.room_name)
            self.my_room = self.room_name_dialog.room_name
            # 切换到房间标签页
            self.tabs.setCurrentIndex(1)
        else:
            # 创建失败，退出等待状态并显示错误
            self.room_name_dialog.exit_waiting_state()
            # 根据错误类型显示不同的提示
            if '重名' in error_message or '已存在' in error_message:
                QMessageBox.warning(self, "创建失败", f"房间名称已存在，请选择其他名称。\n错误信息：{error_message}")
            else:
                QMessageBox.warning(self, "创建失败", f"创建房间失败：{error_message}")

    def _handle_room_create(self, data):
        """处理新房间创建"""
        try:
            room_name, room_info, locked_seats = data.get("data")
            self.logger.info(f'新房间创建: {room_name}')
            # 创建UI
            self.create_room(room_info, locked_seats)
        except Exception as e:
            self.logger.error(f'同步房间创建时出错: {e}')

    def _handle_player_enter_room(self, data):
        """处理玩家进入房间消息"""
        room_name = data.get("room_name")
        player_name = data.get("player_name")

        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            room_widget.add_room_member(player_name)

        if player_name == self.user_data['username']:
            self.my_room = room_name
            self.show_room_tab(room_name)
            self.tabs.setCurrentIndex(1)

        print(f'{player_name}进入房间: {room_name}')

    def _handle_player_leave_room(self, data):
        """处理玩家离开房间消息"""
        room_name = data.get("room_name")
        player_name = data.get("player_name")
        room_remaining = data.get("room_remaining")
        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            room_widget.remove_room_member(player_name)
        print(f'{player_name}离开房间: {room_name}')
        if room_remaining == 0:
            self.remove_room(room_name)
            print(f'{room_name}无人，已删除房间: {room_name}')
        if room_name == self.my_room:
            for i in range(4):
                if self.seats[i].user_data[0] == player_name:
                    self.seats[i].user_data = ['', 0]
                    self.seats[i].release()
    
    def _handle_seat_holded(self, data):
        """处理座位占有消息（增量更新）"""
        seat_index = data.get("seat_index")
        click_user = data.get("click_user")
        room_name = data.get("room_name")
        last_seat = data.get("last_seat")
        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            if click_user == self.user_data['username']:
                self.my_seat = seat_index

            # 更新座位占用信息
            room_widget.seat_locks[seat_index][0] = click_user
            room_widget.seat_locks[seat_index][1] = 0
            if last_seat != None:
                room_widget.seat_locks[last_seat][0] = ''
                room_widget.seat_locks[last_seat][1] = 0

            # 加载新用户的头像（如果需要）
            if click_user:
                room_widget._load_user_avatar(click_user)

            # 更新房间UI显示
            room_widget._update_locked_seats_display()
            room_widget.update()

            # 如果是当前用户所在的房间，同时更新座位选择界面
            if room_name == self.my_room:
                self.seats[seat_index].user_data = [click_user, 0]
                self.seats[seat_index].hold()
                if last_seat != None:
                    self.seats[last_seat].user_data = ['', 0]
                    self.seats[last_seat].release()

            self.logger.debug(f"更新房间 {room_name} 座位占用: {click_user} 占用座位 {seat_index}")

    def _handle_seat_released(self, data):
        """处理座位释放消息（增量更新）"""
        seat_index = data.get("seat_index")
        click_user = data.get("click_user")
        room_name = data.get("room_name")
        room_widget = self.get_room_by_name(room_name)
        if room_widget:
            if click_user == self.user_data['username']:
                self.my_seat = None

            # 更新座位释放信息
            room_widget.seat_locks[seat_index][0] = ''
            room_widget.seat_locks[seat_index][1] = 0

            # 更新房间UI显示
            room_widget._update_locked_seats_display()
            room_widget.update()

            # 如果是当前用户所在的房间，同时更新座位选择界面
            if room_name == self.my_room:
                self.seats[seat_index].user_data = ['', 0]
                self.seats[seat_index].release()

            self.logger.debug(f"更新房间 {room_name} 座位释放: {click_user} 释放座位 {seat_index}")

    def _handle_seat_switch(self, data):
        """处理座位锁定/解锁消息（增量更新）"""
        seat_index = data['seat_code']
        click_user = data['click_user']
        room_name = data['room_name']
        switch = data['switch']
        room_widget = self.get_room_by_name(room_name)
        if room_widget and room_widget.seat_locks[seat_index][0] == click_user:
            # 更新座位锁定状态
            room_widget.seat_locks[seat_index][1] = switch

            # 更新房间UI显示的玩家信息（锁定数量变化）
            room_widget._update_locked_seats_display()

            # 锁定/解锁操作不触发重绘，只更新内部状态
            self.logger.debug(f"更新房间 {room_name} 座位 {seat_index}: {click_user} {'锁定' if switch == 1 else '解锁'}")

            # 如果是当前用户所在的房间，同时更新座位选择界面
            if room_name == self.my_room:
                self.seats[seat_index].user_data = [click_user, switch]
                if switch == 1:
                    self.seats[seat_index].lock()
                else:
                    self.seats[seat_index].unlock()

            # 更新自己的座位锁定状态（如果是自己的锁定/解锁请求回馈）
            if click_user == self.user_data['username']:
                self.I_locked_seat = switch


    '''================登录时通过服务器返回的消息，初始化已有的房间UI================'''

    def load_existing_rooms(self, room_info: dict, room_locked_seats: dict):
        """加载服务器返回的现有房间信息"""
        self.logger.info(f'开始加载现有房间信息: {list(room_info.keys())}')
        # 保存服务器数据到本地副本
        self.room_info = room_info.copy()
        self.room_locked_seats = room_locked_seats.copy()
        self.logger.info(f'已保存房间信息到本地副本，房间数: {len(self.room_info)}')
        self.create_room(room_info, room_locked_seats)


    # 重置调整UI大小及位置
    def resize_ui(self):
        self.logger.debug('开始调整UI大小')
        self.setMinimumSize(int(1500 * self.scale), int(945 * self.scale))
        self.setMaximumSize(int(1500 * self.scale), int(945 * self.scale))
        MainWindow.center(self)  # 调用后面定义的center方法在屏幕居中
        self.hall.setFixedWidth(int(945 * self.scale))
        self.left_stack.setFixedWidth(int(945 * self.scale))
        self.right_widget.setFixedWidth(int(535 * self.scale))
        self.right_top_widget.setFixedSize(int(535 * self.scale), 200)
        self.tabs.setFixedSize(int(535 * self.scale) - 120, 200)
        self.user_frame.setFixedSize(100, 200)
        self.user_frame.move(int(1500 * self.scale - 110), 10)

        # 调整大厅页面标题区域的背景图片
        if hasattr(self, 'hall'):
            scaled_height = int(180 * self.scale)

            # 查找并调整标题区域
            for child in self.hall.findChildren(QWidget):
                if child.parent() == self.hall:
                    # 调整标题区域容器大小
                    child.setFixedHeight(scaled_height)

                    # 调整背景图片QLabel和文字QLabel
                    for label in child.findChildren(QLabel):
                        if label.text() == "游戏大厅":
                            # 标题文字QLabel
                            label.setGeometry(int(50 * self.scale), int(30 * self.scale),
                                            int(400 * self.scale), int(60 * self.scale))
                            # 调整字体大小
                            scaled_font_size = int(33 * self.scale)
                            if scaled_font_size < 25:
                                scaled_font_size = 25
                            label.setStyleSheet(f"font-size: {scaled_font_size}px; font-weight: Bold; color: #aaaaaa; background-color: transparent;")
                        elif label.text() == "欢迎加入超级四国大战":
                            # 副标题文字QLabel
                            label.setGeometry(int(50 * self.scale), int(90 * self.scale),
                                            int(400 * self.scale), int(40 * self.scale))
                            # 调整字体大小
                            scaled_font_size = int(13 * self.scale)
                            if scaled_font_size < 10:
                                scaled_font_size = 10
                            label.setStyleSheet(f"font-size: {scaled_font_size}px; color: #cccccc; background-color: transparent;")
                    break

        self.scene.setSceneRect(0, 0, int(945 * self.scale), int(945 * self.scale))
        # 给场景设置位图做为底图并缩放合适
        bg = QPixmap(r'./client/image/bg.png').scaled(int(945 * self.scale), int(945 * self.scale))
        self.scene.setBackgroundBrush(QBrush(bg))
        # 将矢量棋盘图缩放到合适大小
        wh = self.scene.background_svg.boundingRect()
        self.scene.background_svg.setScale(945 / wh.width() * self.scale)

        self.view.setFixedSize(945 * self.scale, 945 * self.scale)
        self.view_mask.resize(self.view.size())

        # 设置房间区域大小和内部组件
        if hasattr(self, 'seat_choose_frame') and self.seat_choose_frame:
            room_size = int(360 * self.scale)
            self.seat_choose_frame.setFixedSize(room_size, room_size)

            # 居中到 view 中心
            view_width = int(945 * self.scale)
            view_height = int(945 * self.scale)
            center_x = (view_width - room_size) // 2
            center_y = (view_height - room_size) // 2
            self.seat_choose_frame.move(center_x, center_y)

            # 根据房间尺寸计算内部组件
            self.update_room_components(room_size)
        self.logger.info(f'UI调整完成，缩放系数: {self.scale}')

    def update_room_components(self, room_size):
        """根据房间尺寸动态计算内部组件的大小和间距"""
        if not hasattr(self, 'seat_choose_frame') or not self.seat_choose_frame:
            return

        # 计算可用空间（减去边距）
        margin = max(10, int(room_size * 0.02))  # 边距为房间尺寸的5%
        available_space = room_size - 2 * margin

        # 计算间距（3x3网格，2个间距）
        spacing = max(5, int(available_space * 0.02))  # 间距为可用空间的8%

        # 计算每个格子的大小
        seat_size = (available_space - 2 * spacing) // 3
        table_size = seat_size  # 桌子和座位一样大

        # 更新布局设置
        layout = self.seat_choose_frame.layout()
        if layout:
            layout.setContentsMargins(margin, margin, margin, margin)
            layout.setSpacing(spacing)

        # 更新座位尺寸
        if hasattr(self, 'seats'):
            for seat in self.seats:
                seat.setFixedSize(seat_size, seat_size)

        # 更新桌子尺寸和图片
        if hasattr(self, 'table') and self.table:
            self.table.setFixedSize(table_size, table_size)
            pic = QPixmap("./client/image/table.png").scaled(
                self.table.size(),
                aspectMode=Qt.AspectRatioMode.KeepAspectRatio
            )
            self.table.setPixmap(pic)

    # 居中窗口的方法
    @staticmethod
    def center(win):
        # 获取主屏幕对象
        screen = QApplication.primaryScreen()
        # 获取屏幕几何信息
        screen_geometry = screen.geometry()
        # 计算窗口居中位置
        new_x = int((screen_geometry.width() - win.geometry().width()) / 2 - 6)
        new_y = int((screen_geometry.height() - win.geometry().height()) / 2 - 40)
        win.move(new_x, new_y)

    def closeEvent(self, event):
        """重写closeEvent方法，处理窗口关闭事件"""
        self.logger.info('捕获到窗口关闭事件')
        # 弹出确认对话框
        reply = QMessageBox.question(
            self,
            '确认退出',
            '确定要退出游戏吗？',
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        if reply == QMessageBox.StandardButton.Yes:
            self.logger.info('用户确认退出，开始程序关闭流程')
            self.login_widget_on = False
            self.login_widget.close()
            self.logger.info('主窗口关闭事件已接受')
            event.accept()
        else:
            self.logger.info('用户取消退出')
            event.ignore()  # 忽略关闭事件


class Seat(QLabel):
    def __init__(self, seat_code, main_window, send_func):
        super().__init__()
        self.seat_code = seat_code
        self.main_window = main_window
        self.user_data = ['', 0]  # [username, lock_status]
        self.send_func = send_func
        self.room_name = ''
        self.setFrameStyle(QFrame.Shape.Panel | QFrame.Shadow.Raised)
        self.setLineWidth(2)
        self.setMidLineWidth(0)
        # 设置默认深灰色样式
        self.setStyleSheet("background-color: #404040; color: #cccccc;")
        self.setText("空")
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.setFont(QFont("微软雅黑", 30))
        self.setText("空")
        self.setEnabled(True)

    def mousePressEvent(self, e):
        super().mousePressEvent(e)
        print(self.main_window.user_data)
        print(self.user_data)
        if e.button() == Qt.MouseButton.LeftButton and self.main_window.I_locked_seat == False:
            if self.user_data == ['', 0] or self.user_data[0] == self.main_window.user_data['username']:
                message = {
                    'type': 'room_event',
                    'data':{
                        'type': 'seat_click',
                        'seat_code': self.seat_code,
                        'click_user': self.main_window.user_data['username'],
                        'room_name': self.room_name
                    }
                }
                print(f"发送座位点击消息: {message}")
                self.send_func(message)
                print("消息已发送")

    def enterEvent(self, e):
        """鼠标进入事件 - 显示tooltip"""
        super().enterEvent(e)
        if self.user_data[0]:  # 如果座位有人占座
            # 根据锁定状态设置不同的主题色
            is_locked = self.user_data[1] == 1
            tooltip_text = f"座位{self.seat_code + 1}: {self.user_data[0]}"
            if is_locked:
                tooltip_text += " \n(已锁定)"
            else:
                tooltip_text += " \n(占座中)"

            # 计算tooltip位置：3号位在左侧，其他座位在右侧
            seat_rect = self.rect()
            if self.seat_code == 3:
                # 3号位：显示在左侧
                tooltip_pos = self.mapToGlobal(seat_rect.topLeft())
                tooltip_pos.setX(tooltip_pos.x() - 3 - seat_rect.width())  # 向左偏移10px
                tooltip_pos.setY(tooltip_pos.y() + seat_rect.height() // 2)  # 垂直居中
            else:
                # 其他座位：显示在右侧
                tooltip_pos = self.mapToGlobal(seat_rect.topRight())
                tooltip_pos.setX(tooltip_pos.x() + 10)  # 向右偏移10px
                tooltip_pos.setY(tooltip_pos.y() + seat_rect.height() // 2)  # 垂直居中

            CustomTooltip().show_tooltip(tooltip_text, tooltip_pos, is_locked)

    def leaveEvent(self, e):
        """鼠标离开事件 - 隐藏tooltip"""
        super().leaveEvent(e)
        CustomTooltip().hide_tooltip()
    
    def hold(self):
        self.setText(self.user_data[0][0])
        self.setFrameStyle(QFrame.Shape.Panel | QFrame.Shadow.Sunken)
        # 设置为主题绿色
        self.setStyleSheet("background-color: #2fd5c9; color: white;")

    def release(self):
        self.setText("空")
        self.setFrameStyle(QFrame.Shape.Panel | QFrame.Shadow.Raised)
        # 恢复为深灰色
        self.setStyleSheet("background-color: #404040; color: #cccccc;")

    def lock(self):
        # 设置为主题橙色
        self.setStyleSheet("background-color: #a06c44; color: white;")

    def unlock(self):
        # 恢复为主题绿色
        self.setStyleSheet("background-color: #2fd5c9; color: white;")


class GameScene(QGraphicsScene):  # 自定义QGraphicsScene类
    def __init__(self, logger, signal, background_svg: str = ''):
        super().__init__()
        self.logger = logger
        self.signal = signal
        self.logger.info('初始化棋盘场景')
        # 加载棋盘背景图
        self.background_svg = QGraphicsSvgItem(background_svg)
        self.addItem(self.background_svg)
        self.active_flag = True
        self.logger.info('棋盘场景初始化完成')

    def mousePressEvent(self, e):  # 重写场景实例的鼠标按键事件
        # self.update()
        super().mousePressEvent(e)  # 如果不希望鼠标事件被拦截，而导致原本的点击行为被忽略，要调用父类的方法使点击产生原有效果
        scene_pos = e.scenePos()  # 如果重写view中的鼠标事件，必须将view坐标转换成场景坐标：view.mapToScene(event.pos)
        if self.active_flag:
            if e.button() == Qt.MouseButton.LeftButton:
                self.signal.emit('l', scene_pos)  # 将取得的场景点击坐标用信号发送给槽函数
            elif e.button() == Qt.MouseButton.RightButton:
                self.signal.emit('r', scene_pos)

    def freeze(self):
        """冻结棋盘，禁止交互"""
        self.logger.debug('冻结棋盘')
        self.active_flag = False

    def activate(self):
        """激活棋盘，允许交互"""
        self.logger.debug('激活棋盘')
        self.active_flag = True
