"""
UI游戏核心模块
专门处理游戏相关的UI事务和消息处理
"""
from PySide6.QtSvgWidgets import QGraphicsSvgItem
from PySide6.QtCore import QObject, Qt
from PySide6.QtWidgets import QGraphicsSimpleTextItem
from PySide6.QtGui import QFont
from threading import Timer


class GameConfig:
    """游戏基础配置数据"""
    
    # 游戏区域坐标
    AREA_0 = [((45 * 6 + 13) + i * 81, 45 * 6 + 81 * 5 + 7 + j * 45) for i in range(5) for j in range(6)]
    AREA_1 = [(45 * 6 + 81 * 5 - 5 + i * 45, 45 * 6 + 25 + j * 81) for i in range(6) for j in range(5)]
    AREA_2 = [((45 * 6 + 13) + i * 81, 7 + j * 45) for i in range(5) for j in range(6)]
    AREA_3 = [(-5 + i * 45, 45 * 6 + 25 + j * 81) for i in range(6) for j in range(5)]
    AREA_MIDDLE = [(283 + i * 162, 295 + j * 162) for i in range(3) for j in range(3)]
    
    # 所有区域合并
    ALL_AREAS = AREA_0 + AREA_1 + AREA_2 + AREA_3 + AREA_MIDDLE
    
    # 军营坐标
    BARRACKS = [
        (364, 727), (526, 727), (445, 772), (364, 817), (526, 817),
        (85, 376), (175, 376), (130, 457), (85, 538), (175, 538),
        (364, 97), (526, 97), (445, 142), (364, 187), (526, 187),
        (715, 376), (805, 376), (760, 457), (715, 538), (805, 538)
    ]
    
    # 总部坐标
    HEADQUARTERS = [
        (364, 907), (526, 907), (-5, 376), (-5, 538), 
        (364, 7), (526, 7), (895, 376), (895, 538)
    ]
    
    # 死亡区域坐标
    AREA_0_DEAD = [(685 + i * 65, 682 + j * 45) for i in range(4) for j in range(6)]
    AREA_1_DEAD = [(670 + i * 45, 217 - j * 65) for j in range(4) for i in range(6)]
    AREA_2_DEAD = [(205 - i * 65, 232 - j * 45) for i in range(4) for j in range(6)]
    AREA_3_DEAD = [(220 - i * 45, 697 + j * 65) for j in range(4) for i in range(6)]
    
    # 路点坐标
    ROAD_POINT = [(445, 727), (364, 772), (526, 772), (445, 817), (283, 907), (364, 907), (445, 907),
                (526, 907), (607, 907), (715, 457), (760, 538), (760, 376), (805, 457), (895, 619),
                (895, 538), (895, 457), (895, 376), (895, 295), (445, 187), (526, 142), (364, 142),
                (445, 97), (607, 7), (526, 7), (445, 7), (364, 7), (283, 7), (175, 457), (130, 376),
                (130, 538), (85, 457), (-5, 295), (-5, 376), (-5, 457), (-5, 538), (-5, 619)]

    # 转向点坐标
    TURN_POINT = [(283, 682), (607, 682), (670, 619), (670, 295), (283, 232), (607, 232), (220, 295), (220, 619)]

    # 棋子原始缩放因子
    PIECE_ORG_SCALE = 55 / 31
    
    @classmethod
    def get_area_list(cls, area_id: int) -> list:
        """获取指定的区域坐标
                
        Args:
            area_id: 区域ID　-> 0-3: 0-3号区域; 4: 中间区域; 5: 军营; 6: 总部; 7: 所有区域
        """
        areas = [cls.AREA_0, cls.AREA_1, cls.AREA_2, cls.AREA_3]
        if 0 <= area_id < 4:
            return areas[area_id]
        elif area_id == 4:
            return cls.AREA_MIDDLE
        elif area_id == 5:
            return cls.BARRACKS
        elif area_id == 6:
            return cls.HEADQUARTERS
        else:
            return cls.ALL_AREAS
    
    @classmethod
    def get_dead_area_list(cls, area_id: int) -> list:
        """获取指定玩家的死亡区域坐标
        
        Args:
            player_id: 死亡区域ID -> 0-3: 0-3号玩家
        """
        dead_areas = [cls.AREA_0_DEAD, cls.AREA_1_DEAD, cls.AREA_2_DEAD, cls.AREA_3_DEAD]
        if 0 <= area_id < 4:
            return dead_areas[area_id]
        return []
    
    @classmethod
    def get_road_point(cls) -> list:
        """获取路点坐标"""
        return cls.ROAD_POINT
    
    @classmethod
    def get_turn_point(cls) -> list:
        """获取转向点坐标"""
        return cls.TURN_POINT
    
    @classmethod
    def get_piece_org_scale(cls) -> float:
        """获取棋子原始缩放因子"""
        return cls.PIECE_ORG_SCALE



class UIGameCore(QObject):
    """UI游戏核心类
    
    负责处理所有游戏相关的UI逻辑：
    - 游戏事件消息处理
    - 游戏状态管理
    - 游戏UI控制
    """
    
    def __init__(self, main_window, logger):
        """初始化UI游戏核心
        
        Args:
            main_window: 主窗口引用
            logger: 日志记录器
        """
        super().__init__()
        self.main_window = main_window
        self.logger = logger

        self.my_name = None
        self.my_seat = 0
        self.my_last_seat = 0
        self.piece_obj_dic = {}  # 保存所有的棋子对象的字典
        self.piece_info_dict = {}  # 保存所有的棋子信息的字典
        self.my_piece = None
        self.enemy_piece = None
        self.rush_lock = 0  # 棋子行动一次后赋值为1，表示被锁定，不可变更选择
        
        # 连接游戏相关信号
        self._connect_game_signals()
        
        self.logger.info("UI游戏核心模块初始化完成")
    
    def _connect_game_signals(self):
        """连接游戏相关信号"""
        # 连接游戏事件响应信号
        self.main_window.message_hub.signals.game_event_response.connect(self.handle_game_event)
        
        self.logger.debug("游戏信号连接完成")
    
    # ==================== 游戏消息处理方法 ====================
    
    def handle_game_event(self, message):
        """处理游戏事件的统一入口"""
        data = message.get("data", {})
        event_type = data.get("type")
        
        self.logger.info(f"处理游戏事件: {event_type}")
        
        if event_type == "start_game":
            self._handle_game_started(data)
        elif event_type == "pick_pieces":
            self.init_pieces(data)
        elif event_type == "end_game":
            self._handle_game_ended(data)
        elif event_type == "game_action":
            self._handle_game_action(data)
        else:
            self.logger.warning(f"未知的游戏事件类型: {event_type}")
    
    def _handle_game_started(self, data):
        """处理游戏开始消息"""
        self.my_name = self.main_window.user_data['username']
        room_name = data.get("game_room")
        members = data.get("members")
        self.logger.info(f"游戏开始: 房间={room_name}, 成员={members}")

        # 更新房间状态为游戏中
        self.main_window.move_room_to_playing(room_name, members)
        
        # 如果当前用户在游戏中，启动游戏界面
        if self.my_name in members:
            self.my_last_seat = self.my_seat
            self.my_seat = members.index(self.my_name)
            self.main_window.view.rotate(90 * (self.my_seat - self.my_last_seat))
            print('我转了{}度'.format(90 * (self.my_seat - self.my_last_seat)))
            self.start_game(members)
        elif self.main_window.my_room == room_name:
            self.logger.info(f'{room_name}开始游戏了，无座位之人退出房间')
            self.main_window.my_room = ''
            self.main_window.tabs.removeTab(1)
    
    def _handle_game_ended(self, data):
        """处理游戏结束消息"""
        room_name = data.get("game_room")
        members = data.get("members")
        self.logger.info(f"游戏结束: 房间={room_name}, 成员={members}")
        
        # 更新房间状态为等待中
        self.main_window.move_room_to_waiting(room_name)
        
        # 如果当前用户在游戏中，结束游戏界面
        if self.main_window.user_data['username'] in members:
            self.end_game()
    
    def _handle_game_action(self, data):
        """处理游戏动作消息"""
        action_type = data.get("action_type")
        self.logger.info(f"处理游戏动作: {action_type}")
        
        # TODO: 根据具体的游戏动作类型进行处理
        # 例如：棋子移动、攻击、防御等
        pass
    

    # ==================== 游戏状态控制方法 ====================

    def start_game(self, members):
        """游戏开始，移除遮罩"""
        self.logger.info("启动游戏界面")
        self.main_window.scene.freeze()
        
        # 更新游戏状态及初始设置
        self.main_window.I_am_playing = True
        self.my_piece = None
        self.enemy_piece = None
        self.piece_info_dict = {}
        self.piece_obj_dic = {}
        self.rush_lock = 0
        items = self.main_window.scene.items()
        for i in items:
            if i != self.main_window.scene.background_svg:  # 排除棋盘对象
                self.main_window.scene.removeItem(i)
                del i
        self.set_name_label(members)



    def end_game(self):
        """游戏结束，显示遮罩"""
        self.logger.info("结束游戏界面")
        
        # 显示游戏视图遮罩
        self.main_window.view_mask.show()
        
        # 更新游戏状态
        self.main_window.I_am_playing = False
        
        # 显示座位选择框架
        self.main_window.seat_choose_frame.show()
        
        # 显示游戏结束引导标志
        self.main_window.show_guide_signs(3)
        
        # 将房间移动到等待状态
        self.main_window.move_room_to_waiting(self.main_window.my_room)
    
    def pause_game(self):
        """暂停游戏"""
        self.logger.info("暂停游戏")
        # TODO: 实现游戏暂停逻辑
        pass
    
    def resume_game(self):
        """恢复游戏"""
        self.logger.info("恢复游戏")
        # TODO: 实现游戏恢复逻辑
        pass
    
    # ==================== 游戏数据处理方法 ====================

    def update_game_data(self, game_data):
        """更新游戏数据"""
        self.logger.debug(f"更新游戏数据: {len(game_data) if game_data else 0} 项")
        # TODO: 实现游戏数据更新逻辑
        pass

    def sync_game_state(self, game_state):
        """同步游戏状态"""
        self.logger.debug("同步游戏状态")
        # TODO: 实现游戏状态同步逻辑
        pass


    # ==================== 棋盘交互方法 ====================

    def init_pieces(self, data):
        """初始化棋子"""
        self.piece_info_dict = data.get("piece_data")
        self.logger.debug("初始化棋子")
        for i in self.piece_info_dict:
            if self.piece_info_dict[i][0] is None:
                pic_name = '.\\client\\image\\pieces\\blank.svg'
            elif self.piece_info_dict[i][0] == self.main_window.my_seat:  # 只显示己方棋面，隐藏其他棋面
                pic_name = '.\\client\\image\\pieces\\' + self.piece_info_dict[i][1] + '\\' + self.piece_info_dict[i][2] + '.svg'
            else:
                # pic_name = '.\\client\\image\\pieces\\' + self.info_dic[i][1] + '\\' + 'back.svg'
                pic_name = '.\\client\\image\\pieces\\' + self.piece_info_dict[i][1] + '\\' + self.piece_info_dict[i][2] + '.svg'
            piece = Piece(pic_name, i, self.piece_info_dict[i], self)  # 实例化棋子
            self.layout(piece)
        self.main_window.seat_choose_frame.hide() # 隐藏座位选择框架
        self.main_window.view_mask.hide() # 隐藏游戏视图遮罩
        self.main_window.show_guide_signs(1) # 显示游戏开始引导标志


    def layout(self, piece):
        piece.setPos((piece.position[0] * self.main_window.scale + 31 * (GameConfig.get_piece_org_scale() * self.main_window.scale - 1) / 2),
                     (piece.position[1] * self.main_window.scale + 17 * (GameConfig.get_piece_org_scale() * self.main_window.scale - 1) / 2))
        # self.rotate(piece)
        piece.setRotation(-90)

    # def rotate(self, piece):  # 判断棋子的阵营及所处位置，执行正确地旋转
    #     pos = piece.pos
    #     my_code = (lambda x: x if x else 0)(self.owner)
    #     if piece.code is None:  # 对空白棋的旋转策略
    #         if pos in self.areaM or pos in self.which_area(my_code, 0) or pos in self.which_area(my_code, 1):
    #             piece.setRotation(-90 * my_code)  # 在自身、友方区域及中央区域按己方棋子处理
    #         elif pos in self.which_area(my_code, 2):
    #             piece.setRotation(-90 * (my_code - 1))  # 在敌方区域，空白棋子相对己方棋子少转90度
    #     elif pos in self.areaM + self.which_area(piece.code, 0):
    #         piece.setRotation(-90 * piece.code)  # 棋子在自己、中央区域，正常旋转
    #     elif pos in self.which_area((lambda x: x + 1 if x < 3 else 0)(piece.code), 0):
    #         piece.setRotation(-90 * (piece.code - 1))  # 棋子在右手侧敌人区域时，按左手侧敌人的角度旋转
    #     else:
    #         piece.setRotation(-90 * (piece.code + 1))  # 棋子在左手侧敌人区域时，按右手侧敌人的角度旋转


    def handle_scene_click(self, button, scene_pos):
        """处理棋盘点击事件"""
        self.logger.debug(f"棋盘点击: 按钮={button}, 位置=({scene_pos.x()}, {scene_pos.y()})")

        # 只有在游戏进行中才处理点击
        if not self.main_window.I_am_playing:
            return

        # TODO: 实现棋盘点击逻辑
        # 例如：选择棋子、移动棋子、攻击等
        pass

    def update_piece_position(self, piece_id, new_pos):
        """更新棋子位置"""
        self.logger.debug(f"更新棋子位置: {piece_id} -> {new_pos}")
        # TODO: 实现棋子位置更新逻辑
        pass

    def highlight_valid_moves(self, piece_id):
        """高亮显示有效移动位置"""
        self.logger.debug(f"高亮有效移动: {piece_id}")
        # TODO: 实现移动位置高亮逻辑
        pass

    
    def set_name_label(self, player_list):
        """设置玩家名称标签"""
        self.scale = self.main_window.scale
        for i in range(4):
            label = QGraphicsSimpleTextItem()
            label.setBrush(Qt.GlobalColor.black)
            label.setText(player_list[i])
            label.setFont(QFont("黑体", 14 * self.scale))
            self.main_window.scene.addItem(label)
            label.setTransformOriginPoint(60 * self.scale / 2, 20 * self.scale / 2)
            if i == 0:
                label.setPos(380 * self.scale, 655 * self.scale)
                label.setRotation(0)
            elif i == 1:
                label.setPos(634 * self.scale, 523 * self.scale)
                label.setRotation(-90)
            elif i == 2:
                label.setPos(510 * self.scale, 270 * self.scale)
                label.setRotation(180)
            else:
                label.setPos(250 * self.scale, 400 * self.scale)
                label.setRotation(90)


    # ==================== 清理方法 ====================
    
    def cleanup(self):
        """清理资源"""
        self.logger.info("清理UI游戏核心资源")
        
        # 断开信号连接
        try:
            self.main_window.message_hub.signals.game_event_response.disconnect(self.handle_game_event)
        except Exception as e:
            self.logger.warning(f"断开游戏信号连接时出错: {e}")
        
        self.logger.info("UI游戏核心资源清理完成")



class Piece(QGraphicsSvgItem):  # 棋子类，继承于QGraphicsSvgItem类，可绑定矢量图片
    def __init__(self, pic_name, id, data_list, game_core):
        super(Piece, self).__init__(pic_name)
        # self.setOpacity(0.5)
        if data_list[0] is None:
            self.setOpacity(0.5)
        self.id = id
        self.owner = data_list[0]
        self.color = data_list[1]
        self.rank = data_list[2]
        self.position = data_list[3]
        self.status = data_list[4]  # 状态判断标志位，0：死亡　1：存活　2：人质
        self.pick_switch = 0
        self.setTransformOriginPoint(31 / 2, 17 / 2)  # 变换基准点处于物体自身坐标系，影响一切变形效果，包括缩放和旋转
        self.setScale(GameConfig.get_piece_org_scale())  # 先变换为初始大小
        self.setScale(GameConfig.get_piece_org_scale() * game_core.main_window.scale)  # 再变换为所需大小
        game_core.piece_obj_dic[id] = self
        game_core.main_window.scene.addItem(self)  # 将棋子对象置入场景，才能显示出来

    def __del__(self):  # 察看棋子对象是否被删除，释放内存
        print('del', self.id)

